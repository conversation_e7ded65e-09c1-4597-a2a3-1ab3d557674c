#!/bin/bash

# Example usage script for trade_when_generator.py

echo "=== Trade When Expression Generator Example ==="
echo

# Example 1: Using the sample base expression file
echo "Example 1: Using sample base expression file"
echo "Command: python trade_when_generator.py -e 'trade_when(GLB_event).md' -f sample_base_expression.txt -o example1_output.txt"
python trade_when_generator.py -e "trade_when(GLB_event).md" -f sample_base_expression.txt -o example1_output.txt
echo "Output saved to: example1_output.txt"
echo "First 3 lines of output:"
head -3 example1_output.txt
echo
echo "---"
echo

# Example 2: Using a simple expression string
echo "Example 2: Using expression string"
echo "Command: python trade_when_generator.py -e 'trade_when(GLB_event).md' -b 'x = ts_mean(close, 20); ts_rank(x, 100)' -o example2_output.txt"
python trade_when_generator.py -e "trade_when(GLB_event).md" -b "x = ts_mean(close, 20); ts_rank(x, 100)" -o example2_output.txt
echo "Output saved to: example2_output.txt"
echo "First 3 lines of output:"
head -3 example2_output.txt
echo
echo "---"
echo

# Show statistics
echo "Statistics:"
echo "- Events file contains $(python -c "
import re
with open('trade_when(GLB_event).md', 'r') as f:
    content = f.read()
open_events = len(re.findall(r'\"[^\"]+\"', content.split('open_event:')[1].split('close_event:')[0]))
close_events = len(re.findall(r'\"[^\"]+\"', content.split('close_event:')[1]))
print(f'{open_events} open events and {close_events} close events')
print(f'Total combinations: {open_events * close_events}')
")"

echo
echo "=== Examples completed successfully! ==="
