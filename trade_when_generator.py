#!/usr/bin/env python3
"""
Trade When Expression Generator

This script automatically adds trade_when operations to economic expressions.
It parses trade_when syntax and transforms base expressions by wrapping them
in trade_when operations with various entry and exit events.
"""

import re
import argparse
import sys
from typing import List, Tuple, Optional
from pathlib import Path


class TradeWhenGenerator:
    def __init__(self):
        self.open_events = []
        self.close_events = []
    
    def parse_events_file(self, events_file: str) -> None:
        """Parse events from the input file."""
        try:
            with open(events_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Extract open events
            open_match = re.search(r'open_event:\s*"([^"]+(?:",\s*"[^"]+)*)"', content, re.DOTALL)
            if open_match:
                # Split by quotes and commas, clean up
                open_events_str = open_match.group(1)
                self.open_events = [event.strip().strip('"') for event in re.split(r'",\s*"', open_events_str)]
                # Clean up any remaining quotes
                self.open_events = [event.strip('"') for event in self.open_events if event.strip()]
            
            # Extract close events
            close_match = re.search(r'close_event:\s*"([^"]+(?:",\s*"[^"]+)*)"', content, re.DOTALL)
            if close_match:
                close_events_str = close_match.group(1)
                self.close_events = [event.strip().strip('"') for event in re.split(r'",\s*"', close_events_str)]
                # Clean up any remaining quotes
                self.close_events = [event.strip('"') for event in self.close_events if event.strip()]
            
            print(f"Loaded {len(self.open_events)} open events and {len(self.close_events)} close events")
            
        except FileNotFoundError:
            print(f"Error: Events file '{events_file}' not found.")
            sys.exit(1)
        except Exception as e:
            print(f"Error parsing events file: {e}")
            sys.exit(1)
    
    def parse_base_expression(self, expression_text: str) -> Tuple[List[str], str]:
        """
        Parse the base expression and extract variable assignments and final expression.

        Returns:
            Tuple of (variable_assignments, final_expression)
        """
        # Split by semicolons first, then by newlines
        if ';' in expression_text:
            parts = [part.strip() for part in expression_text.split(';') if part.strip()]
        else:
            parts = [line.strip() for line in expression_text.strip().split('\n') if line.strip()]

        variable_assignments = []
        final_expression = ""

        for part in parts:
            # Check if part contains an assignment (has '=' but not in comparison)
            # Look for assignment pattern: variable_name = expression
            if re.match(r'^\s*\w+\s*=\s*[^=<>!]', part):
                variable_assignments.append(part)
            else:
                # This is the final expression
                final_expression = part

        return variable_assignments, final_expression
    
    def generate_trade_when_expression(self, base_expression: str, open_event: str, close_event: str) -> str:
        """Generate a complete trade_when expression."""
        variable_assignments, final_expr = self.parse_base_expression(base_expression)

        # If we have a final expression, create a variable for it
        if final_expr and not final_expr.startswith('trade_when'):
            # Create variable name 'e' for the final expression
            variable_assignments.append(f"e = {final_expr};")
            final_variable = "e"
        else:
            # If no final expression or already trade_when, use the last assignment
            if variable_assignments:
                last_assignment = variable_assignments[-1]
                # Extract variable name from last assignment
                var_match = re.match(r'(\w+)\s*=', last_assignment)
                final_variable = var_match.group(1) if var_match else "e"
            else:
                final_variable = "e"

        # Build the complete expression
        result_lines = []
        for assignment in variable_assignments:
            result_lines.append(assignment)

        # Add the trade_when operation
        trade_when_expr = f"trade_when({open_event}, {final_variable}, {close_event})"
        result_lines.append(trade_when_expr)

        return '\n'.join(result_lines)
    
    def generate_all_combinations(self, base_expression: str, output_file: str) -> None:
        """Generate all combinations of trade_when expressions and write to output file."""
        if not self.open_events or not self.close_events:
            print("Error: No events loaded. Please check the events file format.")
            return
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                total_combinations = len(self.open_events) * len(self.close_events)
                print(f"Generating {total_combinations} combinations...")
                
                count = 0
                for open_event in self.open_events:
                    for close_event in self.close_events:
                        expression = self.generate_trade_when_expression(
                            base_expression, open_event, close_event
                        )
                        # Write as a single line (replace newlines with semicolons for compactness)
                        single_line = expression.replace('\n', '; ')
                        f.write(single_line + '\n')
                        count += 1
                
                print(f"Successfully generated {count} expressions in '{output_file}'")
                
        except Exception as e:
            print(f"Error writing output file: {e}")
            sys.exit(1)


def main():
    parser = argparse.ArgumentParser(
        description="Generate trade_when expressions from base expressions and events",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python trade_when_generator.py -e events.md -b "a = ts_mean(close, 10); -ts_zscore(a, 200)" -o output.txt
  python trade_when_generator.py -e events.md -f base_expression.txt -o output.txt
        """
    )
    
    parser.add_argument('-e', '--events', required=True,
                        help='Path to events file containing open_event and close_event definitions')
    parser.add_argument('-b', '--base-expression', 
                        help='Base expression as string')
    parser.add_argument('-f', '--expression-file',
                        help='Path to file containing base expression')
    parser.add_argument('-o', '--output', required=True,
                        help='Output file path for generated expressions')
    
    args = parser.parse_args()
    
    # Validate input arguments
    if not args.base_expression and not args.expression_file:
        print("Error: Either --base-expression or --expression-file must be provided")
        sys.exit(1)
    
    if args.base_expression and args.expression_file:
        print("Error: Provide either --base-expression or --expression-file, not both")
        sys.exit(1)
    
    # Get base expression
    if args.expression_file:
        try:
            with open(args.expression_file, 'r', encoding='utf-8') as f:
                base_expression = f.read().strip()
        except FileNotFoundError:
            print(f"Error: Expression file '{args.expression_file}' not found.")
            sys.exit(1)
    else:
        base_expression = args.base_expression
    
    # Initialize generator and process
    generator = TradeWhenGenerator()
    generator.parse_events_file(args.events)
    generator.generate_all_combinations(base_expression, args.output)


if __name__ == "__main__":
    main()
