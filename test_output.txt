simple_expr = ts_mean(close, 10); simple_expr * 2; trade_when(rank(vec_avg(mdl109_news_sent_1m)) > 0.8, simple_expr, abs(returns) > 0.1)
simple_expr = ts_mean(close, 10); simple_expr * 2; trade_when(rank(vec_avg(mdl109_news_sent_1m)) > 0.8, simple_expr, -1)
simple_expr = ts_mean(close, 10); simple_expr * 2; trade_when(ts_rank(vec_avg(mdl109_news_sent_1m),22) > 0.8, simple_expr, abs(returns) > 0.1)
simple_expr = ts_mean(close, 10); simple_expr * 2; trade_when(ts_rank(vec_avg(mdl109_news_sent_1m),22) > 0.8, simple_expr, -1)
simple_expr = ts_mean(close, 10); simple_expr * 2; trade_when(rank(vec_avg(nws20_ssc)) > 0.8, simple_expr, abs(returns) > 0.1)
simple_expr = ts_mean(close, 10); simple_expr * 2; trade_when(rank(vec_avg(nws20_ssc)) > 0.8, simple_expr, -1)
simple_expr = ts_mean(close, 10); simple_expr * 2; trade_when(ts_rank(vec_avg(nws20_ssc),22) > 0.8, simple_expr, abs(returns) > 0.1)
simple_expr = ts_mean(close, 10); simple_expr * 2; trade_when(ts_rank(vec_avg(nws20_ssc),22) > 0.8, simple_expr, -1)
simple_expr = ts_mean(close, 10); simple_expr * 2; trade_when(vec_avg(nws20_ssc) > 0, simple_expr, abs(returns) > 0.1)
simple_expr = ts_mean(close, 10); simple_expr * 2; trade_when(vec_avg(nws20_ssc) > 0, simple_expr, -1)
simple_expr = ts_mean(close, 10); simple_expr * 2; trade_when(rank(vec_avg(nws20_bee)) > 0.8, simple_expr, abs(returns) > 0.1)
simple_expr = ts_mean(close, 10); simple_expr * 2; trade_when(rank(vec_avg(nws20_bee)) > 0.8, simple_expr, -1)
simple_expr = ts_mean(close, 10); simple_expr * 2; trade_when(ts_rank(vec_avg(nws20_bee),22) > 0.8, simple_expr, abs(returns) > 0.1)
simple_expr = ts_mean(close, 10); simple_expr * 2; trade_when(ts_rank(vec_avg(nws20_bee),22) > 0.8, simple_expr, -1)
simple_expr = ts_mean(close, 10); simple_expr * 2; trade_when(rank(vec_avg(nws20_qmb)) > 0.8, simple_expr, abs(returns) > 0.1)
simple_expr = ts_mean(close, 10); simple_expr * 2; trade_when(rank(vec_avg(nws20_qmb)) > 0.8, simple_expr, -1)
simple_expr = ts_mean(close, 10); simple_expr * 2; trade_when(ts_rank(vec_avg(nws20_qmb),22) > 0.8, simple_expr, abs(returns) > 0.1)
simple_expr = ts_mean(close, 10); simple_expr * 2; trade_when(ts_rank(vec_avg(nws20_qmb),22) > 0.8, simple_expr, -1)
simple_expr = ts_mean(close, 10); simple_expr * 2; trade_when(ts_arg_max(volume, 5) == 0, simple_expr, abs(returns) > 0.1)
simple_expr = ts_mean(close, 10); simple_expr * 2; trade_when(ts_arg_max(volume, 5) == 0, simple_expr, -1)
simple_expr = ts_mean(close, 10); simple_expr * 2; trade_when(ts_corr(close, volume, 20) < 0, simple_expr, abs(returns) > 0.1)
simple_expr = ts_mean(close, 10); simple_expr * 2; trade_when(ts_corr(close, volume, 20) < 0, simple_expr, -1)
simple_expr = ts_mean(close, 10); simple_expr * 2; trade_when(ts_corr(close, volume, 5) < 0, simple_expr, abs(returns) > 0.1)
simple_expr = ts_mean(close, 10); simple_expr * 2; trade_when(ts_corr(close, volume, 5) < 0, simple_expr, -1)
simple_expr = ts_mean(close, 10); simple_expr * 2; trade_when(ts_mean(volume,10)>ts_mean(volume,60), simple_expr, abs(returns) > 0.1)
simple_expr = ts_mean(close, 10); simple_expr * 2; trade_when(ts_mean(volume,10)>ts_mean(volume,60), simple_expr, -1)
simple_expr = ts_mean(close, 10); simple_expr * 2; trade_when(group_rank(ts_std_dev(returns,60), sector) > 0.7, simple_expr, abs(returns) > 0.1)
simple_expr = ts_mean(close, 10); simple_expr * 2; trade_when(group_rank(ts_std_dev(returns,60), sector) > 0.7, simple_expr, -1)
simple_expr = ts_mean(close, 10); simple_expr * 2; trade_when(ts_zscore(returns,60) > 2, simple_expr, abs(returns) > 0.1)
simple_expr = ts_mean(close, 10); simple_expr * 2; trade_when(ts_zscore(returns,60) > 2, simple_expr, -1)
simple_expr = ts_mean(close, 10); simple_expr * 2; trade_when(ts_arg_min(volume, 5) > 3, simple_expr, abs(returns) > 0.1)
simple_expr = ts_mean(close, 10); simple_expr * 2; trade_when(ts_arg_min(volume, 5) > 3, simple_expr, -1)
simple_expr = ts_mean(close, 10); simple_expr * 2; trade_when(ts_std_dev(returns, 5) > ts_std_dev(returns, 20), simple_expr, abs(returns) > 0.1)
simple_expr = ts_mean(close, 10); simple_expr * 2; trade_when(ts_std_dev(returns, 5) > ts_std_dev(returns, 20), simple_expr, -1)
simple_expr = ts_mean(close, 10); simple_expr * 2; trade_when(ts_arg_max(close, 5) == 0, simple_expr, abs(returns) > 0.1)
simple_expr = ts_mean(close, 10); simple_expr * 2; trade_when(ts_arg_max(close, 5) == 0, simple_expr, -1)
simple_expr = ts_mean(close, 10); simple_expr * 2; trade_when(ts_arg_max(close, 20) == 0, simple_expr, abs(returns) > 0.1)
simple_expr = ts_mean(close, 10); simple_expr * 2; trade_when(ts_arg_max(close, 20) == 0, simple_expr, -1)
simple_expr = ts_mean(close, 10); simple_expr * 2; trade_when(ts_corr(close, volume, 5) > 0, simple_expr, abs(returns) > 0.1)
simple_expr = ts_mean(close, 10); simple_expr * 2; trade_when(ts_corr(close, volume, 5) > 0, simple_expr, -1)
simple_expr = ts_mean(close, 10); simple_expr * 2; trade_when(ts_corr(close, volume, 5) > 0.3, simple_expr, abs(returns) > 0.1)
simple_expr = ts_mean(close, 10); simple_expr * 2; trade_when(ts_corr(close, volume, 5) > 0.3, simple_expr, -1)
simple_expr = ts_mean(close, 10); simple_expr * 2; trade_when(ts_corr(close, volume, 5) > 0.5, simple_expr, abs(returns) > 0.1)
simple_expr = ts_mean(close, 10); simple_expr * 2; trade_when(ts_corr(close, volume, 5) > 0.5, simple_expr, -1)
simple_expr = ts_mean(close, 10); simple_expr * 2; trade_when(ts_corr(close, volume, 20) > 0, simple_expr, abs(returns) > 0.1)
simple_expr = ts_mean(close, 10); simple_expr * 2; trade_when(ts_corr(close, volume, 20) > 0, simple_expr, -1)
simple_expr = ts_mean(close, 10); simple_expr * 2; trade_when(ts_corr(close, volume, 20) > 0.3, simple_expr, abs(returns) > 0.1)
simple_expr = ts_mean(close, 10); simple_expr * 2; trade_when(ts_corr(close, volume, 20) > 0.3, simple_expr, -1)
simple_expr = ts_mean(close, 10); simple_expr * 2; trade_when(ts_corr(close, volume, 20) > 0.5, simple_expr, abs(returns) > 0.1)
simple_expr = ts_mean(close, 10); simple_expr * 2; trade_when(ts_corr(close, volume, 20) > 0.5, simple_expr, -1)
