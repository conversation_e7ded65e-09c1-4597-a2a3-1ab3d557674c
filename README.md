# Trade When Expression Generator

This script automatically adds `trade_when` operations to economic expressions by combining base expressions with entry and exit events.

## Features

- **Parse trade_when syntax**: `trade_when(x, y, z)` where:
  - `x` = entry event (开仓事件)
  - `y` = expression to be processed (被处理的表达式)  
  - `z` = exit event (出仓事件)

- **Transform expressions**: Convert base expressions by wrapping them in trade_when operations
- **Process event lists**: Read from files containing entry and exit events
- **Generate combinations**: Create all possible combinations of entry/exit events
- **Clean output**: One expression per line, no extra formatting

## Usage

### Command Line Options

```bash
python trade_when_generator.py [OPTIONS]

Required arguments:
  -e, --events EVENTS_FILE     Path to events file containing open_event and close_event definitions
  -o, --output OUTPUT_FILE     Output file path for generated expressions

Expression input (choose one):
  -b, --base-expression TEXT   Base expression as string
  -f, --expression-file FILE   Path to file containing base expression
```

### Examples

1. **Using expression string**:
```bash
python trade_when_generator.py \
  -e "trade_when(GLB_event).md" \
  -b "financial_data = vec_min(anl69_best_pe_ratio); gp = group_cartesian_product(country, industry); a = signed_power(group_neutralize(financial_data, gp), 0.1); -ts_zscore(a, 200)" \
  -o output.txt
```

2. **Using expression file**:
```bash
python trade_when_generator.py \
  -e "trade_when(GLB_event).md" \
  -f sample_base_expression.txt \
  -o output.txt
```

## Input Format

### Events File Format
The events file should contain `open_event` and `close_event` definitions:

```
open_event:"event1", "event2", "event3",
close_event:"exit1", "exit2"
```

### Base Expression Format
Base expressions can contain multiple variable assignments followed by a final expression:

```
financial_data = vec_min(anl69_best_pe_ratio);
gp = group_cartesian_product(country, industry);
a = signed_power(group_neutralize(financial_data, gp), 0.1);
-ts_zscore(a, 200)
```

## Output Format

The script generates one expression per line, with all combinations of entry and exit events:

```
financial_data = vec_min(anl69_best_pe_ratio);; gp = group_cartesian_product(country, industry);; a = signed_power(group_neutralize(financial_data, gp), 0.1);; e = -ts_zscore(a, 200);; trade_when(rank(vec_avg(mdl109_news_sent_1m)) > 0.8, e, abs(returns) > 0.1)
financial_data = vec_min(anl69_best_pe_ratio);; gp = group_cartesian_product(country, industry);; a = signed_power(group_neutralize(financial_data, gp), 0.1);; e = -ts_zscore(a, 200);; trade_when(rank(vec_avg(mdl109_news_sent_1m)) > 0.8, e, -1)
...
```

## Files

- `trade_when_generator.py` - Main script
- `sample_base_expression.txt` - Example base expression
- `trade_when(GLB_event).md` - Events definition file
- `generated_expressions.txt` - Example output file

## How It Works

1. **Parse Events**: Extracts open and close events from the events file
2. **Parse Base Expression**: Separates variable assignments from the final expression
3. **Generate Combinations**: Creates trade_when expressions for all event combinations
4. **Output**: Writes each complete expression as a single line

The script automatically:
- Creates an intermediate variable `e` for the final expression
- Combines all variable assignments with the trade_when operation
- Generates all possible combinations of entry and exit events
- Outputs clean, single-line expressions ready for use
