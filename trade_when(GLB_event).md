open_event:"rank(vec_avg(mdl109_news_sent_1m)) > 0.8",
                "ts_rank(vec_avg(mdl109_news_sent_1m),22) > 0.8",
                "rank(vec_avg(nws20_ssc)) > 0.8", "ts_rank(vec_avg(nws20_ssc),22) > 0.8",
                "vec_avg(nws20_ssc) > 0", "rank(vec_avg(nws20_bee)) > 0.8",
                "ts_rank(vec_avg(nws20_bee),22) > 0.8", "rank(vec_avg(nws20_qmb)) > 0.8",
                "ts_rank(vec_avg(nws20_qmb),22) > 0.8","ts_arg_max(volume, 5) == 0", "ts_corr(close, volume, 20) < 0",
            "ts_corr(close, volume, 5) < 0", "ts_mean(volume,10)>ts_mean(volume,60)",
            "group_rank(ts_std_dev(returns,60), sector) > 0.7", "ts_zscore(returns,60) > 2",
            "ts_arg_min(volume, 5) > 3", "ts_std_dev(returns, 5) > ts_std_dev(returns, 20)",
            "ts_arg_max(close, 5) == 0", "ts_arg_max(close, 20) == 0",
            "ts_corr(close, volume, 5) > 0", "ts_corr(close, volume, 5) > 0.3",
            "ts_corr(close, volume, 5) > 0.5", "ts_corr(close, volume, 20) > 0",
            "ts_corr(close, volume, 20) > 0.3", "ts_corr(close, volume, 20) > 0.5",

close_event:"abs(returns) > 0.1", "-1"